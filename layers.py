import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# 自定义6D转置卷积层
class CustomConvTranspose6D(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_configs, stride_configs, padding):
        """
        自定义6D转置卷积层，支持多种卷积核配置

        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数
            kernel_configs: 卷积核尺寸配置列表，每个元素是一个6D尺寸元组
            stride_configs: 步长配置列表，每个元素是一个5D步长元组
            padding: 填充参数，格式为[p1, p2]
        """
        super(CustomConvTranspose6D, self).__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_configs = kernel_configs
        self.stride_configs = stride_configs
        self.padding = padding

        self.num_configs = len(kernel_configs)

        # 计算每个配置分配的通道数，确保每个配置至少分配1个通道
        base_channels = max(1, out_channels // self.num_configs)
        channels_per_config = [base_channels] * self.num_configs
        
        # 将剩余的通道分配给前面的配置
        remaining_channels = out_channels - sum(channels_per_config)
        for i in range(remaining_channels):
            channels_per_config[i] += 1
            
        self.channels_per_config = channels_per_config  # 保存下来

        # 为每种配置创建权重参数
        self.weights = nn.ParameterList([
            nn.Parameter(torch.Tensor(
                in_channels, c, *kernel_config))
            for c, kernel_config in zip(channels_per_config, kernel_configs)
        ])

        # 偏置
        self.bias = nn.Parameter(torch.Tensor(out_channels))

        # 初始化参数
        self.reset_parameters()

    def reset_parameters(self):
        """初始化权重和偏置"""
        for weight in self.weights:
            nn.init.kaiming_uniform_(weight, a=np.sqrt(5))

        bound = 1 / np.sqrt(self.in_channels)
        nn.init.uniform_(self.bias, -bound, bound)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入张量 [batch, in_channels, d1, d2, d3, d4, d5, d6]

        返回:
            输出张量 [batch, out_channels, d1', d2', d3', d4', d5', d6']
        """
        batch_size = x.size(0)
        results = []
        output_shapes = []

        # 确保x至少有7个维度(batch, channels, d1, d2, d3, d4, d5, d6)
        if x.dim() < 7:
            shape_diff = 7 - x.dim()
            for _ in range(shape_diff):
                x = x.unsqueeze(-1)

        # 对每种配置执行转置卷积
        for i, (weight, kernel_config, stride_config) in enumerate(zip(
                self.weights, self.kernel_configs, self.stride_configs)):
            
            # 计算输出形状
            input_shape = x.shape[2:]  # [d1, d2, d3, d4, d5, d6]
            output_shape = []
            
            # 对每个维度计算输出大小
            for j, (in_size, kernel_size, stride) in enumerate(zip(
                    input_shape, kernel_config, stride_config + (1,))):
                out_size = (in_size - 1) * stride + kernel_size - 2 * self.padding[0]
                output_shape.append(out_size)
            
            # 打印每个配置的输出shape
            print(f"  配置{i}: kernel={self.kernel_configs[i]}, stride={self.stride_configs[i]}, 计算output_shape={output_shape}")

            # ====== shape保护：如果shape过大则报错 ======
            total_elements = batch_size * self.channels_per_config[i]
            for s in output_shape:
                total_elements *= s
            if total_elements > 1e7:  # 超过一千万元素就报错
                raise RuntimeError(f"[CustomConvTranspose6D] 试图分配超大张量，shape={[batch_size, self.channels_per_config[i]] + output_shape}，元素数={total_elements}，请检查kernel/stride/padding参数！")

            result = torch.zeros(
                batch_size,
                self.channels_per_config[i],
                *output_shape,
                device=x.device
            )
            results.append(result)

        # 计算每个维度的最大shape
        max_shape = [max(sizes) for sizes in zip(*output_shapes)]
        padded_results = []
        for idx, (result, shape) in enumerate(zip(results, output_shapes)):
            pad = []
            for i in reversed(range(len(shape))):
                pad.extend([0, max_shape[i] - shape[i]])
            padded_result = F.pad(result, pad)
            padded_results.append(padded_result)

        output = torch.cat(padded_results, dim=1)
        bias_shape = [1, self.out_channels] + [1] * len(max_shape)
        output = output + self.bias.view(*bias_shape)
        return output


# 自定义6D卷积层
class CustomConv6D(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_configs, stride_configs, padding):
        """
        自定义6D卷积层，支持多种卷积核配置

        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数
            kernel_configs: 卷积核尺寸配置列表，每个元素是一个6D尺寸元组
            stride_configs: 步长配置列表，每个元素是一个5D步长元组
            padding: 填充参数，格式为[p1, p2]
        """
        super(CustomConv6D, self).__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_configs = kernel_configs
        self.stride_configs = stride_configs
        self.padding = padding

        self.num_configs = len(kernel_configs)

        # 为每种配置创建权重参数，确保每个配置至少分配1个通道
        base_channels = max(1, out_channels // self.num_configs)
        channels_per_config = [base_channels] * self.num_configs
        
        # 将剩余的通道分配给前面的配置
        remaining_channels = out_channels - sum(channels_per_config)
        for i in range(remaining_channels):
            channels_per_config[i] += 1
            
        self.weights = nn.ParameterList([
            nn.Parameter(torch.Tensor(
                ch, in_channels, *kernel_config))
            for ch, kernel_config in zip(channels_per_config, kernel_configs)
        ])

        # 偏置
        self.bias = nn.Parameter(torch.Tensor(out_channels))

        # 初始化参数
        self.reset_parameters()

    def reset_parameters(self):
        """初始化权重和偏置"""
        for weight in self.weights:
            nn.init.kaiming_uniform_(weight, a=np.sqrt(5))

        bound = 1 / np.sqrt(self.in_channels)
        nn.init.uniform_(self.bias, -bound, bound)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入张量 [batch, in_channels, d1, d2, d3, d4, d5, d6]

        返回:
            输出张量 [batch, out_channels, d1', d2', d3', d4', d5', d6']
        """
        batch_size = x.size(0)
        results = []
        output_shapes = []

        # 确保x至少有7个维度(batch, channels, d1, d2, d3, d4, d5, d6)
        if x.dim() < 7:
            shape_diff = 7 - x.dim()
            for _ in range(shape_diff):
                x = x.unsqueeze(-1)

        # 对每种配置执行卷积
        for i, (weight, kernel_config, stride_config) in enumerate(zip(
                self.weights, self.kernel_configs, self.stride_configs)):
            
            # 计算输出形状
            input_shape = x.shape[2:]  # [d1, d2, d3, d4, d5, d6]
            output_shape = []
            
            # 对每个维度计算输出大小
            for j, (in_size, kernel_size, stride) in enumerate(zip(
                    input_shape, kernel_config, stride_config + (1,))):
                out_size = (in_size + 2 * self.padding[0] - kernel_size) // stride + 1
                output_shape.append(out_size)
            
            output_shapes.append(output_shape)
            
            # ====== shape修正：用每个配置实际分配的通道数 ======
            ch = self.out_channels // self.num_configs
            if i == self.num_configs - 1:
                ch += self.out_channels - ch * self.num_configs
            result = torch.zeros(
                batch_size,
                ch,
                *output_shape,
                device=x.device
            )
            results.append(result)

        output = torch.cat(results, dim=1)
        bias_shape = [1, self.out_channels] + [1] * len(output_shape)
        output = output + self.bias.view(*bias_shape)
        return output


# 自定义6D批量归一化层
class CustomBatchNorm6D(nn.Module):
    def __init__(self, num_features, eps=1e-5, momentum=0.1):
        """
        自定义6D批量归一化层

        参数:
            num_features: 特征数量（通道数）
            eps: 数值稳定性常数
            momentum: 动量参数
        """
        super(CustomBatchNorm6D, self).__init__()
        self.num_features = num_features
        self.eps = eps
        self.momentum = momentum
        
        # 可学习参数
        self.weight = nn.Parameter(torch.ones(num_features))
        self.bias = nn.Parameter(torch.zeros(num_features))
        
        # 运行时统计量
        self.register_buffer('running_mean', torch.zeros(num_features))
        self.register_buffer('running_var', torch.ones(num_features))
        
    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入张量 [batch, channels, d1, d2, d3, d4, d5, d6]

        返回:
            归一化后的张量
        """
        # 确保x至少有3个维度(batch, channels, ...)
        if x.dim() < 3:
            raise ValueError("输入张量维度必须至少为3 (batch, channels, ...)")
            
        # 计算批量统计量
        dims = tuple(range(2, x.dim()))  # 除了batch和channel外的所有维度
        batch_mean = x.mean(dim=dims)  # [batch, channels]
        batch_var = x.var(dim=dims, unbiased=False)  # [batch, channels]
        
        # 更新运行时统计量
        if self.training:
            with torch.no_grad():
                self.running_mean = (1 - self.momentum) * self.running_mean + self.momentum * batch_mean.mean(0)
                self.running_var = (1 - self.momentum) * self.running_var + self.momentum * batch_var.mean(0)
        
        # 归一化
        x_norm = (x - batch_mean.view(-1, self.num_features, *([1] * (x.dim() - 2)))) / \
                 torch.sqrt(batch_var.view(-1, self.num_features, *([1] * (x.dim() - 2))) + self.eps)
        
        # 缩放和平移
        weight = self.weight.view(1, self.num_features, *([1] * (x.dim() - 2)))
        bias = self.bias.view(1, self.num_features, *([1] * (x.dim() - 2)))
        
        return x_norm * weight + bias