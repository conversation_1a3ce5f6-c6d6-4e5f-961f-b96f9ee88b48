#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的CGAN模型
"""

import torch
import numpy as np
from models import Generator, Discriminator

def test_discriminator_fix():
    """测试判别器的维度修复"""
    print("测试判别器维度修复...")
    
    # 设置参数
    batch_size = 4
    feature_dim = 5
    embed_dim = 16
    
    # 创建测试数据
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建判别器
    input_shape = (feature_dim, 1, 1, 1, 1, 1)
    discriminator = Discriminator(input_shape=input_shape, embed_dim=embed_dim).to(device)
    
    # 测试不同维度的输入
    print("\n测试不同维度的输入:")
    
    # 测试2D输入 [batch, features]
    print("1. 测试2D输入 [batch, features]:")
    real_samples_2d = torch.randn(batch_size, feature_dim, device=device)
    conditions = torch.randint(0, 3, (batch_size,), device=device)
    print(f"   输入形状: {real_samples_2d.shape}")
    print(f"   条件形状: {conditions.shape}")
    
    try:
        output_2d = discriminator(real_samples_2d, conditions)
        print(f"   输出形状: {output_2d.shape}")
        print("   ✓ 2D输入测试成功!")
    except Exception as e:
        print(f"   ✗ 2D输入测试失败: {e}")
        return False
    
    # 测试3D输入 [batch, channels, features]
    print("\n2. 测试3D输入 [batch, channels, features]:")
    real_samples_3d = torch.randn(batch_size, 1, feature_dim, device=device)
    print(f"   输入形状: {real_samples_3d.shape}")
    
    try:
        output_3d = discriminator(real_samples_3d, conditions)
        print(f"   输出形状: {output_3d.shape}")
        print("   ✓ 3D输入测试成功!")
    except Exception as e:
        print(f"   ✗ 3D输入测试失败: {e}")
        return False
    
    # 测试6D输入 [batch, channels, d1, d2, d3, d4]
    print("\n3. 测试6D输入 [batch, channels, d1, d2, d3, d4]:")
    real_samples_6d = torch.randn(batch_size, 1, feature_dim, 1, 1, 1, device=device)
    print(f"   输入形状: {real_samples_6d.shape}")
    
    try:
        output_6d = discriminator(real_samples_6d, conditions)
        print(f"   输出形状: {output_6d.shape}")
        print("   ✓ 6D输入测试成功!")
    except Exception as e:
        print(f"   ✗ 6D输入测试失败: {e}")
        return False
    
    print("\n所有测试通过! 判别器维度修复成功!")
    return True

def test_generator_discriminator_integration():
    """测试生成器和判别器的集成"""
    print("\n" + "="*50)
    print("测试生成器和判别器集成...")
    
    # 设置参数
    batch_size = 4
    latent_dim = 100
    feature_dim = 5
    embed_dim = 16
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    generator = Generator(latent_dim=latent_dim, embed_dim=embed_dim, output_dim=feature_dim).to(device)
    discriminator = Discriminator(input_shape=(feature_dim, 1, 1, 1, 1, 1), embed_dim=embed_dim).to(device)
    
    # 创建输入数据
    z = torch.randn(batch_size, latent_dim, device=device)
    conditions = torch.randint(0, 3, (batch_size,), device=device)
    
    print(f"潜在向量形状: {z.shape}")
    print(f"条件形状: {conditions.shape}")
    
    try:
        # 生成样本
        fake_samples = generator(z, conditions)
        print(f"生成样本形状: {fake_samples.shape}")

        # 检查生成样本的详细信息
        print(f"生成样本维度: {fake_samples.dim()}")
        print(f"生成样本数据类型: {fake_samples.dtype}")

        # 判别生成样本
        fake_outputs = discriminator(fake_samples, conditions)
        print(f"判别器输出形状: {fake_outputs.shape}")

        print("✓ 生成器和判别器集成测试成功!")
        return True

    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试CGAN模型修复...")
    
    # 测试判别器修复
    success1 = test_discriminator_fix()
    
    # 测试集成
    success2 = test_generator_discriminator_integration()
    
    if success1 and success2:
        print("\n" + "="*50)
        print("🎉 所有测试通过! 修复成功!")
        print("现在可以正常训练CGAN模型了。")
    else:
        print("\n" + "="*50)
        print("❌ 测试失败，需要进一步调试。")
