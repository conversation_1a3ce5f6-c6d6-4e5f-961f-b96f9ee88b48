import torch
import numpy as np
import scipy.io
import os

def test_generator(generator, num_samples=10, latent_dim=100, device=None):
    """
    测试生成器功能

    参数:
        generator: 训练好的生成器模型
        num_samples: 要生成的样本数量
        latent_dim: 潜在空间维度
        device: 运行设备
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    generator.eval()
    generator.to(device)

    with torch.no_grad():
        # 生成随机潜在向量
        z = torch.randn(num_samples, latent_dim, device=device)
        # 生成随机条件
        conditions = torch.randint(0, 3, (num_samples,), device=device)

        # 使用生成器生成样本
        fake_samples = generator(z, conditions)

        print(f"生成了{num_samples}个样本，形状: {fake_samples.shape}")

        # 打印前几个样本的统计数据
        for i in range(min(3, num_samples)):
            sample = fake_samples[i]
            print(f"样本 {i + 1}:")
            print(f"  最大值: {torch.abs(sample).max().item():.4f}")
            print(f"  均值: {torch.abs(sample).mean().item():.4f}")
            print(f"  标准差: {torch.std(torch.abs(sample)).item():.4f}")

        return fake_samples, conditions


def save_to_mat(samples, conditions, filename='generated_samples.mat'):
    """
    将生成的样本保存到MAT文件

    参数:
        samples: 生成的样本 [num_samples, feature_dim]
        conditions: 用于生成的条件 [num_samples, condition_dim]
        filename: 输出的文件名
    """
    # 转换为numpy数组
    samples_np = samples.detach().cpu().numpy()
    conditions_np = conditions.detach().cpu().numpy()

    # 创建字典并保存
    save_dict = {
        'generated_coefficients': samples_np,
        'conditions': conditions_np
    }

    scipy.io.savemat(filename, save_dict)
    print(f"生成的样本已保存到: {filename}")


def combine_all_orders(generators, latent_dim=100, num_samples=100, device=None):
    """
    组合所有阶数的生成结果
    
    参数:
        generators: 训练好的生成器列表，每个元素对应一个阶数
        latent_dim: 潜在空间维度
        num_samples: 要生成的样本数量
        device: 运行设备
    
    返回:
        组合后的完整系数
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 使用相同的潜在向量和条件，确保一致性
    z = torch.randn(num_samples, latent_dim, device=device)
    conditions = torch.randint(0, 3, (num_samples,), device=device)
    
    all_coeffs = []
    
    for i, generator in enumerate(generators):
        n = i + 2  # 当前阶数
        generator.eval()
        generator.to(device)
        
        with torch.no_grad():
            # 生成当前阶数的系数
            order_coeffs = generator(z, conditions)
            all_coeffs.append(order_coeffs.cpu().numpy())
            
        print(f"已生成n={n}阶系数，形状: {order_coeffs.shape}")
    
    # 组合所有阶数的系数
    combined = np.concatenate(all_coeffs, axis=1)
    print(f"组合后的完整系数形状: {combined.shape}")
    
    return combined, conditions