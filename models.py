import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from layers import CustomConvTranspose6D, CustomConv6D, CustomBatchNorm6D

# 生成器模型
class Generator(nn.Module):
    def __init__(self, latent_dim=100, embed_dim=16, output_dim=256):
        super(Generator, self).__init__()
        self.embed_condition = nn.Embedding(3, embed_dim)  # 3类，嵌入16维
        self.project_z = nn.Linear(latent_dim, 2048)
        self.z_reshape_dim = (128, 4, 4, 1, 1, 1)
        combined_channels = 128 + embed_dim
        self.tconv1 = CustomConvTranspose6D(
            combined_channels, 128,
            kernel_configs=[
                (2, 12, 12, 12, 12, 1),
                (2, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1)
            ],
            padding=[0, 0]
        )
        self.bn1 = CustomBatchNorm6D(128)
        self.tconv2 = CustomConvTranspose6D(
            128, 96,
            kernel_configs=[
                (2, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1)
            ],
            padding=[0, 0]
        )
        self.bn2 = CustomBatchNorm6D(96)
        self.tconv3 = CustomConvTranspose6D(
            96, 64,
            kernel_configs=[
                (2, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1)
            ],
            padding=[0, 0]
        )
        self.bn3 = CustomBatchNorm6D(64)
        self.tconv4 = CustomConvTranspose6D(
            64, 32,
            kernel_configs=[
                (2, 13, 15, 16, 12, 1),
                (3, 13, 14, 13, 13, 1),
                (3, 15, 15, 15, 15, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 2),
                (2, 2, 2, 2, 2),
                (2, 2, 2, 2, 1)
            ],
            padding=[1, 0]
        )
        self.bn4 = CustomBatchNorm6D(32)
        self.tconv5 = CustomConvTranspose6D(
            32, 1,
            kernel_configs=[
                (5, 14, 14, 14, 14, 1),
                (3, 13, 14, 13, 13, 1),
                (3, 13, 15, 17, 15, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1)
            ],
            padding=[1, 0]
        )
        
        # 添加输出维度调整层
        self.output_projection = nn.Linear(2048, output_dim)  # 假设卷积层输出2048维

    def forward(self, z, s):
        batch_size = z.size(0)
        cond_embedded = self.embed_condition(s)  # [batch, 16]
        z_projected = self.project_z(z)
        z_reshaped = z_projected.view(batch_size, 128, 4, 4, 1, 1, 1)
        # reshape cond_embedded为6D，和z_reshaped拼接
        cond_reshaped = cond_embedded.view(batch_size, 16, 1, 1, 1, 1, 1)
        x = torch.cat([z_reshaped, cond_reshaped], dim=1)
        x = self.tconv1(x)
        x = F.relu(self.bn1(x))
        x = self.tconv2(x)
        x = F.relu(self.bn2(x))
        x = self.tconv3(x)
        x = F.relu(self.bn3(x))
        x = self.tconv4(x)
        x = F.relu(self.bn4(x))
        x = self.tconv5(x)
        x_flat = x.view(batch_size, -1)
        out = self.output_projection(x_flat)
        return out


# 判别器模型
class Discriminator(nn.Module):
    def __init__(self, input_shape, embed_dim=16):
        super(Discriminator, self).__init__()
        self.embed_condition = nn.Embedding(3, embed_dim)
        self.embed_dim = embed_dim  # 添加这一行，保存embed_dim作为类属性
        self.input_shape = input_shape
        self.conv1 = CustomConv6D(
            1 + embed_dim, 256,  # 拼接类别嵌入
            kernel_configs=[
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 15, 15, 14, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1)
            ],
            padding=[1, 0]
        )
        self.conv2 = CustomConv6D(
            256, 128,
            kernel_configs=[
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 15, 15, 15, 1)
            ],
            stride_configs=[
                (1, 1, 2, 2, 2),
                (2, 2, 2, 2, 2),
                (2, 2, 2, 2, 1)
            ],
            padding=[1, 0]
        )
        self.conv3 = CustomConv6D(
            128, 64,
            kernel_configs=[
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 15, 15, 15, 1)
            ],
            stride_configs=[
                (1, 1, 2, 2, 2),
                (2, 2, 2, 2, 2),
                (2, 2, 2, 2, 1)
            ],
            padding=[1, 0]
        )
        self.conv4 = CustomConv6D(
            64, 32,
            kernel_configs=[
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 15, 15, 15, 1)
            ],
            stride_configs=[
                (1, 1, 2, 2, 2),
                (2, 2, 2, 2, 2),
                (2, 2, 2, 2, 1)
            ],
            padding=[1, 0]
        )
        self.conv5 = CustomConv6D(
            32, 1,
            kernel_configs=[
                (3, 13, 13, 13, 13, 1),
                (3, 13, 13, 13, 13, 1),
                (3, 13, 15, 15, 15, 1)
            ],
            stride_configs=[
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1),
                (1, 1, 1, 1, 1)
            ],
            padding=[1, 0]
        )

    def forward(self, x, s):
        batch_size = x.size(0)
        
        # 获取输入张量的维度
        input_dims = x.dim()
        
        # 嵌入条件
        cond_embedded = self.embed_condition(s)  # [batch, embed_dim]
        
        # 重塑条件嵌入以匹配输入张量的维度
        # 创建一个与输入张量维度匹配的形状
        reshape_dims = [batch_size, self.embed_dim] + [1] * (input_dims - 2)
        cond_reshaped = cond_embedded.view(*reshape_dims)
        
        # 扩展条件嵌入以匹配输入张量的空间维度
        if input_dims > 2:
            # 获取输入张量的空间维度
            spatial_dims = x.shape[2:]
            # 扩展条件嵌入到相同的空间维度
            cond_expanded = cond_reshaped.expand(batch_size, self.embed_dim, *spatial_dims)
        else:
            cond_expanded = cond_reshaped
        
        # 连接输入和条件
        x = torch.cat([x, cond_expanded], dim=1)
        
        # 继续处理
        x = F.leaky_relu(self.conv1(x), 0.2)
        x = F.leaky_relu(self.conv2(x), 0.2)
        x = F.leaky_relu(self.conv3(x), 0.2)
        x = F.leaky_relu(self.conv4(x), 0.2)
        x = self.conv5(x)
        x = torch.mean(x, dim=tuple(range(2, x.dim())))
        return x


# 自定义损失函数
class GeneratorLoss(nn.Module):
    def __init__(self):
        super(GeneratorLoss, self).__init__()
        self.loss = nn.BCEWithLogitsLoss()

    def forward(self, fake_outputs, real_labels):
        return self.loss(fake_outputs, real_labels)


class DiscriminatorLoss(nn.Module):
    def __init__(self):
        super(DiscriminatorLoss, self).__init__()
        self.loss = nn.BCEWithLogitsLoss()

    def forward(self, real_outputs, fake_outputs, real_labels, fake_labels):
        real_loss = self.loss(real_outputs, real_labels)
        fake_loss = self.loss(fake_outputs, fake_labels)
        return real_loss + fake_loss
