import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from layers import CustomConvTranspose6D, CustomConv6D, CustomBatchNorm6D

# 生成器模型
class Generator(nn.Module):
    def __init__(self, latent_dim=100, embed_dim=16, output_dim=256):
        super(Generator, self).__init__()
        self.embed_condition = nn.Embedding(3, embed_dim)  # 3类，嵌入16维
        self.output_dim = output_dim

        # 使用简单的全连接层替代复杂的6D转置卷积
        self.fc1 = nn.Linear(latent_dim + embed_dim, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, output_dim)

        self.dropout = nn.Dropout(0.3)

    def forward(self, z, s):
        batch_size = z.size(0)

        # 嵌入条件
        cond_embedded = self.embed_condition(s)  # [batch, embed_dim]

        # 连接潜在向量和条件嵌入
        z_combined = torch.cat([z, cond_embedded], dim=1)  # [batch, latent_dim + embed_dim]

        # 通过全连接层生成输出
        x = F.relu(self.fc1(z_combined))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)

        return x


# 判别器模型
class Discriminator(nn.Module):
    def __init__(self, input_shape, embed_dim=16):
        super(Discriminator, self).__init__()
        self.embed_condition = nn.Embedding(3, embed_dim)
        self.embed_dim = embed_dim
        self.input_shape = input_shape

        # 获取输入特征维度
        if isinstance(input_shape, tuple) and len(input_shape) > 0:
            self.feature_dim = input_shape[0]
        else:
            self.feature_dim = input_shape

        # 使用简单的全连接层替代复杂的6D卷积
        # 这样可以避免维度不匹配的问题
        self.fc1 = nn.Linear(self.feature_dim + embed_dim, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, 1)

        self.dropout = nn.Dropout(0.3)

    def forward(self, x, s):
        batch_size = x.size(0)

        # 处理不同维度的输入，将其展平为2D
        if x.dim() == 2:
            # 如果输入已经是2D [batch, features]，直接使用
            x_flat = x
        elif x.dim() == 3:
            # 如果输入是3D [batch, channels, features]，展平后两个维度
            x_flat = x.view(batch_size, -1)
        elif x.dim() > 3:
            # 如果输入是更高维度，展平除batch维度外的所有维度
            x_flat = x.view(batch_size, -1)
        else:
            raise ValueError(f"输入张量维度不支持: {x.dim()}D")

        # 嵌入条件
        cond_embedded = self.embed_condition(s)  # [batch, embed_dim]

        # 连接输入特征和条件嵌入
        x_combined = torch.cat([x_flat, cond_embedded], dim=1)  # [batch, feature_dim + embed_dim]

        # 通过全连接层
        x = F.leaky_relu(self.fc1(x_combined), 0.2)
        x = self.dropout(x)
        x = F.leaky_relu(self.fc2(x), 0.2)
        x = self.dropout(x)
        x = F.leaky_relu(self.fc3(x), 0.2)
        x = self.dropout(x)
        x = F.leaky_relu(self.fc4(x), 0.2)
        x = self.fc5(x)

        return x


# 自定义损失函数
class GeneratorLoss(nn.Module):
    def __init__(self):
        super(GeneratorLoss, self).__init__()
        self.loss = nn.BCEWithLogitsLoss()

    def forward(self, fake_outputs, real_labels):
        return self.loss(fake_outputs, real_labels)


class DiscriminatorLoss(nn.Module):
    def __init__(self):
        super(DiscriminatorLoss, self).__init__()
        self.loss = nn.BCEWithLogitsLoss()

    def forward(self, real_outputs, fake_outputs, real_labels, fake_labels):
        real_loss = self.loss(real_outputs, real_labels)
        fake_loss = self.loss(fake_outputs, fake_labels)
        return real_loss + fake_loss
