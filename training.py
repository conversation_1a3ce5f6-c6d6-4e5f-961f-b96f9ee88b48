import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np

# 训练函数
def train(generator, discriminator, dataloader, num_epochs=100, latent_dim=100, device=None):
    """
    训练CGAN模型

    参数:
        generator: 生成器模型
        discriminator: 判别器模型
        dataloader: 数据加载器
        num_epochs: 训练轮数
        latent_dim: 潜在空间维度
        device: 运行设备
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    generator.to(device)
    discriminator.to(device)
    
    # 优化器
    g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
    d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
    
    # 损失函数
    criterion = nn.BCEWithLogitsLoss()
    
    # 训练循环
    for epoch in range(num_epochs):
        g_losses = []
        d_losses = []
        
        for i, (real_samples, conditions) in enumerate(dataloader):
            batch_size = real_samples.size(0)
            real_samples = real_samples.to(device)
            conditions = conditions.to(device)
            
            # 创建真实和虚假标签
            real_labels = torch.ones(batch_size, 1, device=device)
            fake_labels = torch.zeros(batch_size, 1, device=device)
            
            # ===== 训练判别器 =====
            d_optimizer.zero_grad()
            
            # 真实样本的判别结果
            real_outputs = discriminator(real_samples.unsqueeze(1), conditions)
            d_real_loss = criterion(real_outputs, real_labels)
            
            # 生成虚假样本
            z = torch.randn(batch_size, latent_dim, device=device)
            fake_samples = generator(z, conditions)
            
            # 虚假样本的判别结果
            fake_outputs = discriminator(fake_samples.detach().unsqueeze(1), conditions)
            d_fake_loss = criterion(fake_outputs, fake_labels)
            
            # 判别器总损失
            d_loss = d_real_loss + d_fake_loss
            d_loss.backward()
            d_optimizer.step()
            
            # ===== 训练生成器 =====
            g_optimizer.zero_grad()
            
            # 生成虚假样本并判别
            fake_outputs = discriminator(fake_samples.unsqueeze(1), conditions)
            g_loss = criterion(fake_outputs, real_labels)
            
            g_loss.backward()
            g_optimizer.step()
            
            # 记录损失
            g_losses.append(g_loss.item())
            d_losses.append(d_loss.item())
        
        # 打印每个epoch的平均损失
        if epoch % 5 == 0 or epoch == num_epochs - 1:
            print(f"Epoch [{epoch+1}/{num_epochs}], "
                  f"D Loss: {sum(d_losses)/len(d_losses):.4f}, "
                  f"G Loss: {sum(g_losses)/len(g_losses):.4f}")
    
    return generator, discriminator