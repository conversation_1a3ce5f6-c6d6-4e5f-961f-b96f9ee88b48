import torch
import numpy as np
import os
import scipy.io
import h5py
from models import Generator, Discriminator
from layers import CustomConvTranspose6D, CustomConv6D, CustomBatchNorm6D
from dataset import MATDataset, split_sh_coeffs_from_n2
from training import train  # 只导入train函数
from utils import save_to_mat, test_generator, combine_all_orders
import torch.optim as optim
import torch.nn as nn

def save_split_data(split_data, filename='split_data.npz'):
    """保存分割后的数据到文件"""
    save_dict = {}
    for i, data in enumerate(split_data):
        save_dict[f'order_{i+2}'] = data  # n=2开始
    
    np.savez(filename, **save_dict)
    print(f"分割后的数据已保存到: {filename}")

def load_split_data(filename='split_data.npz'):
    """从文件加载分割后的数据"""
    try:
        loaded = np.load(filename, allow_pickle=True)
        split_data = []
        
        # 按顺序加载数据
        for i in range(14):  # n=2到n=15
            key = f'order_{i+2}'
            if key in loaded:
                split_data.append(loaded[key])
            else:
                print(f"警告: 文件中缺少{key}数据")
        
        print(f"成功从{filename}加载分割后的数据")
        return split_data
    except Exception as e:
        print(f"加载分割数据失败: {e}")
        return None

def train_model(generator, discriminator, dataloader, num_epochs=100, latent_dim=100, device=None):
    """
    训练CGAN模型

    参数:
        generator: 生成器模型
        discriminator: 判别器模型
        dataloader: 数据加载器
        num_epochs: 训练轮数
        latent_dim: 潜在空间维度
        device: 运行设备
        
    返回:
        训练后的生成器和判别器模型
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    generator.to(device)
    discriminator.to(device)
    
    # 优化器
    g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
    d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
    
    # 损失函数
    criterion = nn.BCEWithLogitsLoss()
    
    # 训练循环
    for epoch in range(num_epochs):
        g_losses = []
        d_losses = []
        
        for i, (real_samples, conditions) in enumerate(dataloader):
            batch_size = real_samples.size(0)
            real_samples = real_samples.to(device)
            conditions = conditions.to(device)
            
            # 创建真实和虚假标签
            real_labels = torch.ones(batch_size, 1, device=device)
            fake_labels = torch.zeros(batch_size, 1, device=device)
            
            # ===== 训练判别器 =====
            d_optimizer.zero_grad()
            
            # 真实样本的判别结果
            real_outputs = discriminator(real_samples, conditions)
            d_real_loss = criterion(real_outputs, real_labels)

            # 生成虚假样本
            z = torch.randn(batch_size, latent_dim, device=device)
            fake_samples = generator(z, conditions)

            # 虚假样本的判别结果
            fake_outputs = discriminator(fake_samples.detach(), conditions)
            d_fake_loss = criterion(fake_outputs, fake_labels)
            
            # 判别器总损失
            d_loss = d_real_loss + d_fake_loss
            d_loss.backward()
            d_optimizer.step()
            
            # ===== 训练生成器 =====
            g_optimizer.zero_grad()
            
            # 生成虚假样本并判别
            fake_outputs = discriminator(fake_samples, conditions)
            g_loss = criterion(fake_outputs, real_labels)
            
            g_loss.backward()
            g_optimizer.step()
            
            # 记录损失
            g_losses.append(g_loss.item())
            d_losses.append(d_loss.item())
        
        # 打印每个epoch的平均损失
        if epoch % 5 == 0 or epoch == num_epochs - 1:
            print(f"Epoch [{epoch+1}/{num_epochs}], "
                  f"D Loss: {sum(d_losses)/len(d_losses):.4f}, "
                  f"G Loss: {sum(g_losses)/len(g_losses):.4f}")
    
    return generator, discriminator
# 训练所有阶数的模型
def train_all_orders(split_data, epochs=50, batch_size=32, latent_dim=100, device=None):
    """
    训练所有阶数的CGAN模型
    
    参数:
        split_data: 分割后的数据列表，每个元素是一个阶数的数据 [n阶系数数, num_samples]
        epochs: 训练轮数
        batch_size: 批次大小
        latent_dim: 潜在空间维度
        device: 运行设备
    
    返回:
        训练好的生成器模型列表
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    trained_generators = []
    
    for order_idx, order_data in enumerate(split_data):
        n = order_idx + 2  # 当前阶数
        print(f"\n{'='*20} 开始训练 n={n} 阶 CGAN模型 {'='*20}")
        print(f"n={n}阶数据形状: {order_data.shape}")
        
        # 转置数据以符合(samples, features)格式
        data = order_data.T  # [num_samples, num_coeffs]
        print(f"转置后数据形状: {data.shape}")
        
        # 处理复数数据：如果是复数，取绝对值
        if np.iscomplex(data).any():
            print("检测到复数数据，取绝对值...")
            data = np.abs(data)
        
        # 为当前阶数生成新的条件（类别标签）
        num_samples = data.shape[0]
        conditions_n = torch.randint(0, 3, (num_samples,), dtype=torch.long)  # 随机类别0,1,2
        print(f"为n={n}阶生成{num_samples}个样本的条件标签")
        
        # 构建Dataset和DataLoader
        if isinstance(data, np.ndarray):
            data_tensor = torch.tensor(data, dtype=torch.float32)
        else:
            data_tensor = data.clone().detach().float()
        
        dataset = MATDataset(data_tensor, conditions_n)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)
        print(f"数据集大小: {len(dataset)}, 批次数: {len(dataloader)}")
        
        # 初始化模型 - 修正输入输出维度
        embed_dim = 16
        input_shape = (data.shape[1], 1, 1, 1, 1, 1)  # 输入特征维度
        output_dim = data.shape[1]  # 输出维度应该等于当前阶数的系数数量
        
        # 创建生成器和判别器
        generator = Generator(latent_dim=latent_dim, embed_dim=embed_dim, output_dim=output_dim)
        discriminator = Discriminator(input_shape=input_shape, embed_dim=embed_dim)
        print(f"模型初始化完成，输入特征维度: {data.shape[1]}, 输出维度: {output_dim}")
        
        # 训练模型
        print(f"开始训练，epoch数: {epochs}")
        # 使用train_model而不是train
        generator, discriminator = train_model(generator, discriminator, dataloader, num_epochs=epochs, latent_dim=latent_dim, device=device)
        
        # 保存模型
        print(f"保存n={n}阶模型...")
        torch.save(generator.state_dict(), f'generator_n{n}.pth')
        torch.save(discriminator.state_dict(), f'discriminator_n{n}.pth')
        print(f"n={n}阶模型保存完成！")
        
        # 生成并保存样本
        print(f"生成n={n}阶测试样本...")
        num_test_samples = 100
        z = torch.randn(num_test_samples, latent_dim, device=device)
        s = torch.randint(0, 3, (num_test_samples,), device=device)  # 随机类别
        with torch.no_grad():
            fake_samples = generator(z, s).detach().cpu().numpy()
        np.save(f'fake_samples_n{n}.npy', fake_samples)
        print(f"n={n}阶测试样本已保存，形状: {fake_samples.shape}")
        
        trained_generators.append(generator)
        print(f"{'='*20} n={n} 阶训练完成 {'='*20}")
    
    print("\n" + "=" * 60)
    print("所有阶数训练完成！")
    print("=" * 60)
    print("保存的模型文件:")
    for n in range(2, 16):
        print(f"  - generator_n{n}.pth")
        print(f"  - discriminator_n{n}.pth")
        print(f"  - fake_samples_n{n}.npy")
    print("=" * 60)
    
    return trained_generators

# 直接指定文件路径并读取
file_path = "E:\\CGAN-pytorch\\ganregenecoeffinew553final.mat"

try:
    # 首先尝试使用scipy.io.loadmat读取(适用于v7.3以下版本)
    data = scipy.io.loadmat(file_path)
    # 删除mat文件中的元信息变量
    keys_to_remove = ['__header__', '__version__', '__globals__']
    for key in keys_to_remove:
        if key in data:
            del data[key]

    print(f"成功使用scipy.io加载文件: {file_path}")
    print("包含以下变量:")
    for key in data:
        try:
            print(f"  {key}: 形状{data[key].shape}, 类型{data[key].dtype}")
        except:
            print(f"  {key}: 无法获取形状信息")

except NotImplementedError:
    # 如果scipy.io无法读取，可能是v7.3或以上版本的mat文件
    try:
        print(f"尝试使用h5py读取高版本mat文件...")
        f = h5py.File(file_path, 'r')

        # 创建一个字典来存储数据
        data = {}

        # 递归函数，用于遍历HDF5文件结构
        def extract_data(obj, path="", data_dict=None):
            if data_dict is None:
                data_dict = {}
            for key, val in obj.items():
                new_path = f"{path}/{key}" if path else key

                if isinstance(val, h5py.Group):
                    # 如果是组，递归处理
                    extract_data(val, new_path, data_dict)
                elif isinstance(val, h5py.Dataset):
                    # 如果是数据集，保存到字典中
                    # 注意：在高版本mat中，matlab的数组是按列存储的，需要转置
                    value = np.array(val)

                    # 检查是否包含参考(对象引用)
                    if val.dtype == np.dtype('object'):
                        refs = value.flat[0]
                        if isinstance(refs, h5py.Reference):
                            value = np.array(f[refs])

                    # 检查是否需要转置
                    if 'MATLAB_class' in val.attrs:
                        matlab_class_attr = val.attrs['MATLAB_class']
                        if isinstance(matlab_class_attr, bytes):
                            matlab_class = matlab_class_attr.decode('utf-8')
                        elif isinstance(matlab_class_attr, np.ndarray) and matlab_class_attr.dtype.type is np.bytes_:
                            matlab_class = matlab_class_attr.tobytes().decode('utf-8')
                        else:
                            matlab_class = str(matlab_class_attr)
                        if matlab_class in ['double', 'single', 'int8', 'int16', 'int32', 'int64',
                                            'uint8', 'uint16', 'uint32', 'uint64']:
                            if len(value.shape) > 1:
                                value = value.T

                    # 检查是否为复数数据
                    if val.dtype == np.dtype('complex64') or val.dtype == np.dtype('complex128'):
                        value = value.view(np.complex128)

                    # 赋值前判断value是否为None
                    if value is not None:
                        data_dict[new_path.replace('/', '.')] = value
            return data_dict

        # 从根开始提取数据
        data = extract_data(f, data_dict=data)

        f.close()

        print(f"成功使用h5py加载文件: {file_path}")
        print("包含以下变量:")
        for key in data:
            try:
                print(f"  {key}: 形状{data[key].shape}, 类型{data[key].dtype}")
            except:
                print(f"  {key}: 无法获取形状信息")

    except Exception as e:
        print(f"使用h5py读取文件失败: {e}")
        data = None

except Exception as e:
    print(f"读取文件失败: {e}")
    data = None

# 现在data变量中包含了.mat文件的内容
if data is not None:
    print("\nMAT文件读取成功!")

    coeffs_key = next((k for k in data.keys() if 'coef' in k.lower()), None)
    if coeffs_key:
        coeffs_data = data[coeffs_key]
        print(f"使用变量: {coeffs_key}，形状: {coeffs_data.shape}")

        # 注意：现在我们理解到 coeffs_data 的形状是 (256, 553)
        # 256 是系数向量长度，553 是样本数量
        # 需要转置，使其变为 (553, 256)，这样每行是一个样本
        coeffs_data_original = coeffs_data  # 保存原始形状用于分割
        coeffs_data = coeffs_data.T  # 转置数据
        print(f"转置后形状: {coeffs_data.shape}")

        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")

# 主程序
if __name__ == '__main__':
    # 确保data和device已定义
    if 'data' not in locals() or data is None:
        print("数据未成功加载，请检查文件路径")
        exit()
    
    if 'device' not in locals():
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
    
    if 'coeffs_data_original' not in locals():
        print("系数数据未成功加载")
        exit()
    
    # 检查是否存在训练好的模型文件
    all_models_exist = True
    for n in range(2, 16):
        if not os.path.exists(f'generator_n{n}.pth'):
            all_models_exist = False
            break

    if all_models_exist:
        print("\n找到所有训练好的模型!")
        
        # 加载所有模型
        latent_dim = 100
        embed_dim = 16
        generators = []
        
        for n in range(2, 16):
            # 计算当前阶数的输出维度
            output_dim = 2*n+1
            
            # 创建生成器
            generator = Generator(latent_dim=latent_dim, embed_dim=embed_dim, output_dim=output_dim)
            
            # 加载模型权重
            generator.load_state_dict(torch.load(f'generator_n{n}.pth', map_location=device))
            generator.eval()
            
            generators.append(generator)
            print(f"已加载n={n}阶生成器模型")
        
        # 组合所有阶数的生成结果
        num_test_samples = 100
        combined_coeffs, test_conditions = combine_all_orders(
            generators, 
            latent_dim=latent_dim, 
            num_samples=num_test_samples, 
            device=device
        )
        
        # 保存组合后的结果
        save_dict = {
            'combined_coefficients': combined_coeffs,
            'conditions': test_conditions.cpu().numpy()
        }
        scipy.io.savemat('combined_coefficients.mat', save_dict)
        print("组合后的系数已保存到: combined_coefficients.mat")
        
    else:
        # 如果没有现成的模型但有数据，则进行训练
        print("\n开始CGAN多阶训练流程")
        print("=" * 60)
        
        # 首先尝试加载已保存的分割数据
        split_data_file = 'split_data.npz'
        if os.path.exists(split_data_file):
            print(f"找到已保存的分割数据: {split_data_file}")
            split_data = load_split_data(split_data_file)
            if split_data is None:
                # 如果加载失败，重新分割
                print("加载失败，重新分割数据...")
                split_data = split_sh_coeffs_from_n2(coeffs_data_original)
                # 保存分割后的数据
                save_split_data(split_data, split_data_file)
        else:
            # 如果没有找到已保存的分割数据，重新分割
            print("未找到已保存的分割数据，开始分割...")
            split_data = split_sh_coeffs_from_n2(coeffs_data_original)
            # 保存分割后的数据
            save_split_data(split_data, split_data_file)
        
        # 训练所有阶数的模型
        trained_generators = train_all_orders(
            split_data,  # 使用分割后的数据
            epochs=50,   # 可以根据需要调整
            batch_size=32, 
            latent_dim=100,
            device=device
        )
        
        print(f"已成功训练{len(trained_generators)}个CGAN模型，对应n=2到n=15阶")

# 添加保存和加载分割数据的函数
def save_split_data(split_data, filename='split_data.npz'):
    """保存分割后的数据到文件"""
    save_dict = {}
    for i, data in enumerate(split_data):
        save_dict[f'order_{i+2}'] = data  # n=2开始
    
    np.savez(filename, **save_dict)
    print(f"分割后的数据已保存到: {filename}")

def load_split_data(filename='split_data.npz'):
    """从文件加载分割后的数据"""
    try:
        loaded = np.load(filename, allow_pickle=True)
        split_data = []
        
        # 按顺序加载数据
        for i in range(14):  # n=2到n=15
            key = f'order_{i+2}'
            if key in loaded:
                split_data.append(loaded[key])
            else:
                print(f"警告: 文件中缺少{key}数据")
        
        print(f"成功从{filename}加载分割后的数据")
        return split_data
    except Exception as e:
        print(f"加载分割数据失败: {e}")
        return None

# 添加一个新的训练函数，直接在CGAN.py中实现
def train_model(generator, discriminator, dataloader, num_epochs=100, latent_dim=100, device=None):
    """
    训练CGAN模型

    参数:
        generator: 生成器模型
        discriminator: 判别器模型
        dataloader: 数据加载器
        num_epochs: 训练轮数
        latent_dim: 潜在空间维度
        device: 运行设备
        
    返回:
        训练后的生成器和判别器模型
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    generator.to(device)
    discriminator.to(device)
    
    # 优化器
    g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
    d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
    
    # 损失函数
    criterion = nn.BCEWithLogitsLoss()
    
    # 训练循环
    for epoch in range(num_epochs):
        g_losses = []
        d_losses = []
        
        for i, (real_samples, conditions) in enumerate(dataloader):
            batch_size = real_samples.size(0)
            real_samples = real_samples.to(device)
            conditions = conditions.to(device)
            
            # 创建真实和虚假标签
            real_labels = torch.ones(batch_size, 1, device=device)
            fake_labels = torch.zeros(batch_size, 1, device=device)
            
            # ===== 训练判别器 =====
            d_optimizer.zero_grad()
            
            # 真实样本的判别结果
            real_outputs = discriminator(real_samples, conditions)
            d_real_loss = criterion(real_outputs, real_labels)

            # 生成虚假样本
            z = torch.randn(batch_size, latent_dim, device=device)
            fake_samples = generator(z, conditions)

            # 虚假样本的判别结果
            fake_outputs = discriminator(fake_samples.detach(), conditions)
            d_fake_loss = criterion(fake_outputs, fake_labels)

            # 判别器总损失
            d_loss = d_real_loss + d_fake_loss
            d_loss.backward()
            d_optimizer.step()

            # ===== 训练生成器 =====
            g_optimizer.zero_grad()

            # 生成虚假样本并判别
            fake_outputs = discriminator(fake_samples, conditions)
            g_loss = criterion(fake_outputs, real_labels)
            
            g_loss.backward()
            g_optimizer.step()
            
            # 记录损失
            g_losses.append(g_loss.item())
            d_losses.append(d_loss.item())
        
        # 打印每个epoch的平均损失
        if epoch % 5 == 0 or epoch == num_epochs - 1:
            print(f"Epoch [{epoch+1}/{num_epochs}], "
                  f"D Loss: {sum(d_losses)/len(d_losses):.4f}, "
                  f"G Loss: {sum(g_losses)/len(g_losses):.4f}")
    
    return generator, discriminator
