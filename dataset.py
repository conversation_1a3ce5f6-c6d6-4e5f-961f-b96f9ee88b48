import torch
import numpy as np
from torch.utils.data import Dataset

# 数据集类
class MATDataset(Dataset):
    def __init__(self, data, conditions):
        """
        MAT文件数据集

        参数:
            data: 数据张量 [num_samples, feature_dim]
            conditions: 条件标签 [num_samples]
        """
        self.data = data
        self.conditions = conditions
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx], self.conditions[idx]


# 分割SH系数函数
def split_sh_coeffs_from_n2(coeffs_matrix):
    """
    coeffs_matrix: [256, num_samples]
    返回: list，每个元素为[n阶系数数, num_samples]，n=2~15
    """
    print("开始分割SH系数数据...")
    n_list = list(range(2, 16))  # n=2~15
    num_per_n = [2*n+1 for n in n_list]
    start_idx = sum([2*n+1 for n in range(0, 2)])  # 跳过n=0,1
    print(f"跳过n=0,1阶，从索引{start_idx}开始分割（对应n=2阶）")
    split_data = []
    idx = start_idx
    for i, (n, num) in enumerate(zip(n_list, num_per_n)):
        split_data.append(coeffs_matrix[idx:idx+num, :])  # shape: [num, num_samples]
        print(f"  n={n}阶: 系数数量={num}, 数据形状={coeffs_matrix[idx:idx+num, :].shape}")
        idx += num
    print(f"分割完成！共{len(split_data)}阶（n=2~15），每阶数据已准备就绪")
    return split_data  # list of [num, num_samples] for each n